<template>
  <div class="app-container">
    <div class="success-page">
      <!-- 成功状态展示 -->
      <div class="success-header">
        <el-result
          icon="success"
          title="申请成功"
          sub-title="授权码信息如下，请下载"
          class="success-result"
        >
          <template #icon>
            <div class="success-icon">
              <el-icon size="64" color="var(--el-color-success)">
                <svg viewBox="0 0 1024 1024">
                  <path fill="currentColor" d="M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896zm-55.808 536.384-99.52-99.584a38.4 38.4 0 1 0-54.336 54.336l126.72 126.72a38.272 38.272 0 0 0 54.336 0l262.4-262.464a38.4 38.4 0 1 0-54.272-54.336L456.192 600.384z"/>
                </svg>
              </el-icon>
            </div>
          </template>
        </el-result>
      </div>

      <!-- 信息展示卡片 -->
      <el-card class="info-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span class="card-title">授权码详细信息</span>
            <el-tag type="success" size="small">已生成</el-tag>
          </div>
        </template>

        <!-- 基本信息 -->
        <div class="info-section">
          <h4 class="section-title">基本信息</h4>
          <el-descriptions
            class="basic-info"
            :column="2"
            direction="horizontal"
            border
            size="default"
          >
            <el-descriptions-item label="销售订单编号" label-class-name="info-label">
              <el-text class="info-value" type="primary">{{ data.salesOrderNo }}</el-text>
            </el-descriptions-item>
            <el-descriptions-item label="项目销售" label-class-name="info-label">
              <el-text class="info-value">{{ data.sellerName }}</el-text>
            </el-descriptions-item>
            <el-descriptions-item label="销售项目名称" label-class-name="info-label" :span="2">
              <el-text class="info-value">{{ data.salesProjectName }}</el-text>
            </el-descriptions-item>
            <el-descriptions-item label="最终客户名称" label-class-name="info-label" :span="2">
              <el-text class="info-value">{{ data.finalCustomerName }}</el-text>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 授权信息 -->
        <div class="info-section">
          <h4 class="section-title">授权信息</h4>
          <el-descriptions
            class="license-info"
            :column="2"
            direction="horizontal"
            border
            size="default"
          >
            <el-descriptions-item label="授权类型" label-class-name="info-label">
              <el-tag type="info" size="small">{{ data.licenseType }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="有效期" label-class-name="info-label">
              <el-text class="info-value" type="warning">{{ data.validType }}</el-text>
            </el-descriptions-item>
            <el-descriptions-item label="账号并发登录数量" label-class-name="info-label">
              <el-text class="info-value" type="success">{{ data.terminalLicenseCount }}</el-text>
            </el-descriptions-item>
            <el-descriptions-item label="授权产品信息" label-class-name="info-label">
              <el-text class="info-value">{{ data.licenseProductInfo }}</el-text>
            </el-descriptions-item>
            <el-descriptions-item label="授权码信息" label-class-name="info-label" :span="2">
              <el-text class="info-value code-text">{{ data.licenseCodeInfo }}</el-text>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 申请信息 -->
        <div class="info-section">
          <h4 class="section-title">申请信息</h4>
          <el-descriptions
            class="apply-info"
            :column="2"
            direction="horizontal"
            border
            size="default"
          >
            <el-descriptions-item label="申请人" label-class-name="info-label">
              <el-text class="info-value">{{ data.applicant }}</el-text>
            </el-descriptions-item>
            <el-descriptions-item label="申请时间" label-class-name="info-label">
              <el-text class="info-value">{{ data.applyDate }}</el-text>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-card>

      <!-- 操作按钮 -->
      <div class="action-footer">
        <el-button
          type="primary"
          size="large"
          :loading="downloadLoading"
          @click="downloadLicensePDF()"
          class="download-btn"
        >
          <el-icon class="mr-2">
            <svg viewBox="0 0 1024 1024">
              <path fill="currentColor" d="M160 832h704a32 32 0 1 1 0 64H160a32 32 0 1 1 0-64zm384-253.696 236.288-236.352 45.248 45.248L508.8 704 192 387.2l45.248-45.248L480 584.704V96h64v482.304z"/>
            </svg>
          </el-icon>
          下载授权说明书
        </el-button>
        <el-button size="large" @click="goBack()" class="back-btn">
          <el-icon class="mr-2">
            <svg viewBox="0 0 1024 1024">
              <path fill="currentColor" d="M224 480h640a32 32 0 1 1 0 64H224a32 32 0 0 1 0-64z"/>
              <path fill="currentColor" d="m237.248 512 265.408 265.344a32 32 0 0 1-45.312 45.312l-288-288a32 32 0 0 1 0-45.312l288-288a32 32 0 1 1 45.312 45.312L237.248 512z"/>
            </svg>
          </el-icon>
          返回
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import Base64 from "@/utils/base64";
  import { useRoute, useRouter } from "vue-router";
  import LicenseCodeAPI from "@/api/license-manage/license-code.api";
  import { downloadFile } from "@/utils";

  const route = useRoute();
  const router = useRouter();
  const data = Base64.decode(route.query.info);

  // 下载文件
  const downloadLoading = ref(false);
  const downloadLicensePDF = () => {
    downloadLoading.value = true;
    LicenseCodeAPI.downloadLicenseFile(data.id).then((response) => {
      downloadFile(response.data, "授权使用说明书.pdf");
      downloadLoading.value = false;
    });
  };

  // 返回
  const goBack = () => {
    router.back();
  };
</script>

<style scoped>
/* 页面特定样式覆盖 */
:deep(.info-label) {
  font-weight: 600;
  color: var(--el-text-color-regular);
  background-color: var(--el-fill-color-light);
}

/* 响应式设计覆盖 */
@media (max-width: 768px) {
  :deep(.el-descriptions) {
    --el-descriptions-item-bordered-label-background: var(--el-fill-color-light);
  }

  :deep(.basic-info),
  :deep(.license-info),
  :deep(.apply-info) {
    --el-descriptions-table-border: 1px solid var(--el-border-color-lighter);
  }

  :deep(.basic-info .el-descriptions__body),
  :deep(.license-info .el-descriptions__body),
  :deep(.apply-info .el-descriptions__body) {
    display: block;
  }

  :deep(.basic-info .el-descriptions__body .el-descriptions__row),
  :deep(.license-info .el-descriptions__body .el-descriptions__row),
  :deep(.apply-info .el-descriptions__body .el-descriptions__row) {
    display: block;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }

  :deep(.basic-info .el-descriptions__body .el-descriptions__row:last-child),
  :deep(.license-info .el-descriptions__body .el-descriptions__row:last-child),
  :deep(.apply-info .el-descriptions__body .el-descriptions__row:last-child) {
    border-bottom: none;
  }

  :deep(.basic-info .el-descriptions__cell),
  :deep(.license-info .el-descriptions__cell),
  :deep(.apply-info .el-descriptions__cell) {
    display: block;
    width: 100% !important;
    padding: 12px 16px;
    border-right: none !important;
  }

  :deep(.basic-info .el-descriptions__cell.is-bordered-label),
  :deep(.license-info .el-descriptions__cell.is-bordered-label),
  :deep(.apply-info .el-descriptions__cell.is-bordered-label) {
    border-bottom: 1px solid var(--el-border-color-lighter);
    background-color: var(--el-fill-color-light);
    font-weight: 600;
  }
}

@media (max-width: 480px) {
  :deep(.success-icon .el-icon) {
    font-size: 48px !important;
  }
}
</style>

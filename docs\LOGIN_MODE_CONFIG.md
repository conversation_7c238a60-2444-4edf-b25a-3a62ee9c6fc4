# 登录方式配置说明

## 概述

项目支持两种登录方式，通过环境变量 `VITE_LOGIN_MODE` 进行控制：

- `local`: 本地账号密码登录（显示登录表单）
- `external`: 外部登录页面（跳转到统一身份认证平台）

## 配置方式

### 1. 开发环境配置

在 `.env.development` 文件中设置：

```bash
# 本地登录模式
VITE_LOGIN_MODE=local

# 或者外部登录模式
VITE_LOGIN_MODE=external
```

### 2. 生产环境配置

在 `.env.production` 文件中设置：

```bash
# 通常生产环境使用外部登录
VITE_LOGIN_MODE=external
```

### 3. UAT环境配置

在 `.env.uat` 文件中设置：

```bash
# UAT环境使用外部登录
VITE_LOGIN_MODE=external
```

## 登录模式详解

### 本地登录模式 (local)

- 显示传统的用户名密码登录表单
- 支持注册、忘记密码等功能
- 适用于开发环境和独立部署的系统

### 外部登录模式 (external)

- 自动跳转到外部统一身份认证平台
- 显示加载动画和跳转提示
- 需要配置以下环境变量：
  - `VITE_APP_BASE_URL`: 应用基础URL
  - `VITE_SID_BASE_URL`: 统一身份认证平台URL

## 环境变量说明

| 变量名 | 说明 | 示例值 |
|--------|------|--------|
| `VITE_LOGIN_MODE` | 登录方式 | `local` 或 `external` |
| `VITE_APP_BASE_URL` | 应用基础URL（外部登录时使用） | `https://fwt.gongjubu.com/nse-cloud` |
| `VITE_SID_BASE_URL` | 统一身份认证平台URL | `https://sid-uat.ruijie.com.cn` |

## 使用示例

### 开发环境使用本地登录

```bash
# .env.development
VITE_LOGIN_MODE=local
```

### 生产环境使用外部登录

```bash
# .env.production
VITE_LOGIN_MODE=external
VITE_APP_BASE_URL=https://your-app-domain.com
VITE_SID_BASE_URL=https://your-sso-domain.com
```

## 注意事项

1. 修改环境变量后需要重启开发服务器
2. 外部登录模式需要确保 `VITE_APP_BASE_URL` 和 `VITE_SID_BASE_URL` 配置正确
3. 本地登录模式下，外部登录相关的环境变量可以不配置
4. 建议在开发环境使用本地登录，生产环境使用外部登录

## 故障排除

### 问题1：外部登录跳转失败

**解决方案**：
- 检查 `VITE_APP_BASE_URL` 和 `VITE_SID_BASE_URL` 是否配置正确
- 确认统一身份认证平台是否可访问

### 问题2：本地登录表单不显示

**解决方案**：
- 确认 `VITE_LOGIN_MODE` 设置为 `local`
- 重启开发服务器

### 问题3：环境变量不生效

**解决方案**：
- 确认环境变量名称正确（必须以 `VITE_` 开头）
- 重启开发服务器
- 检查对应环境的配置文件是否存在

# 应用端口
VITE_APP_PORT=3000
# 项目名称
VITE_APP_TITLE=网络仿真环境
# 代理前缀
VITE_APP_BASE_API=/dev-api

# 接口地址
# VITE_APP_API_URL=https://api.youlai.tech # 线上
VITE_APP_API_URL=http://localhost:8081    # 本地

# WebSocket 端点（不配置则关闭），线上 ws://api.youlai.tech/ws ，本地 ws://localhost:8989/ws
VITE_APP_WS_ENDPOINT=

# 启用 Mock 服务
VITE_MOCK_DEV_SERVER=false

VITE_APP_BASE_URL=https://fwt.gongjubu.com/nse-cloud
VITE_SID_BASE_URL=https://sid-uat.ruijie.com.cn

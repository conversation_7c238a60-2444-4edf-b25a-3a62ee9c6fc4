<template>
  <div class="app-container">
    <el-card header="授权绑定申请">
      <div class="form-container">
        <el-form ref="applyFormRef" :model="formData" :rules="rules" label-width="140px">
          <el-form-item label="授权码" prop="licenseCode">
            <el-input
              ref="licenseCodeInputRef"
              v-model="formData.licenseCode"
              placeholder="请输入授权书上的授权码"
              clearable
            />
          </el-form-item>

          <el-form-item label="销售订单编号" prop="salesOrderNo">
            <el-input v-model="formData.salesOrderNo" placeholder="请输入销售订单编号" clearable />
          </el-form-item>

          <el-form-item label="销售项目名称" prop="salesProjectName">
            <el-input
              v-model="formData.salesProjectName"
              placeholder="请输入销售项目名称"
              clearable
            />
          </el-form-item>

          <el-form-item label="最终客户名称" prop="finalCustomerName">
            <el-input
              v-model="formData.finalCustomerName"
              placeholder="请输入最终客户名称"
              clearable
            />
          </el-form-item>

          <el-form-item label="项目销售" prop="sellerName">
            <el-input v-model="formData.sellerName" placeholder="请输入项目销售" clearable />
          </el-form-item>

          <el-form-item label="服务器机器码" prop="machineCode">
            <el-input
              v-model="formData.machineCode"
              placeholder="请输入服务器机器码，部署成功后从授权管理页面获取"
              clearable
            />
          </el-form-item>

          <el-form-item label="服务器注销码" prop="machineCancelCode">
            <el-input
              v-model="formData.machineCancelCode"
              placeholder="请输入服务器注销码，非必填，针对授权变更情况下使用"
              clearable
            />
          </el-form-item>
        </el-form>
        <div>
          <el-button type="primary" @click="handleSubmit()" :loading="submitLoading">
            绑 定
          </el-button>
          <el-button @click="cancelApply()">取 消</el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive } from "vue";
  import LicenseFileAPI from "@/api/license-manage/license-file.api";
  import { ApplyForm } from "@/api/license-manage/types/license-file.type";
  import LicenseCodeAPI from "@/api/license-manage/license-code.api";
  import { useRouter } from "vue-router";
  import Base64 from "@/utils/base64";
  const router = useRouter();
  const submitLoading = ref(false);
  const applyFormRef = ref();
  const formData = reactive<ApplyForm>({
    licenseCode: "",
    salesOrderNo: "",
    salesProjectName: "",
    finalCustomerName: "",
    sellerName: "",
    machineCode: "",
  });

  const licenseCodeInputRef = ref();

  let hasCancelCode = false;
  // 表单校验
  const rules = reactive({
    licenseCode: [
      { required: true, message: "请输入授权码", trigger: "blur" },
      {
        // 获取授权码回显信息
        validator: (rule: any, value: string, callback: any) => {
          LicenseCodeAPI.salesInfoByCode(value).then((res) => {
            if (!res) {
              callback("授权码不存在！");
            }
            LicenseFileAPI.getMachineCode(value).then((machineCode) => {
              if (machineCode && !hasCancelCode) {
                callback(`该授权码已被机器码${machineCode}绑定，请确认！`);
              }
              formData.salesOrderNo = res.salesOrderNo;
              formData.salesProjectName = res.salesProjectName;
              formData.finalCustomerName = res.finalCustomerName;
              formData.sellerName = res.sellerName;
              callback();
            });
          });
        },
        trigger: "blur",
      },
    ],
    salesOrderNo: [{ required: true, message: "请输入销售订单编号", trigger: "blur" }],
    salesProjectName: [{ required: true, message: "请输入销售项目名称", trigger: "blur" }],
    finalCustomerName: [{ required: true, message: "请输入最终客户名称", trigger: "blur" }],
    sellerName: [{ required: true, message: "请输入项目销售", trigger: "blur" }],
    machineCode: [{ required: true, message: "请输入服务器机器码", trigger: "blur" }],
    machineCancelCode: [
      {
        // 获取授权码回显信息
        validator: (rule: any, value: string, callback: any) => {
          hasCancelCode = false;
          if (!value) {
            callback();
            return;
          }
          hasCancelCode = true;
          if (!formData.licenseCode) {
            callback();
            return;
          }
          LicenseFileAPI.isCancelCodeCorrect(formData.licenseCode, value).then((correct) => {
            if (!correct) {
              callback("注销码不正确！");
              return;
            }
            licenseCodeInputRef.value.focus();
            licenseCodeInputRef.value.blur();
            callback();
          });
        },
        trigger: "blur",
      },
    ],
  });

  // 提交授权绑定申请表单
  const handleSubmit = () => {
    applyFormRef.value.validate((valid: boolean) => {
      if (valid) {
        submitLoading.value = true;
        LicenseFileAPI.apply(toRaw(formData)).then((res) => {
          router.push({
            path: "/license-file/apply-success",
            query: {
              info: Base64.encode(
                JSON.stringify({
                  id: res.id,
                  salesOrderNo: res.salesOrderNo,
                  salesProjectName: res.salesProjectName,
                  finalCustomerName: res.finalCustomerName,
                  sellerName: res.sellerName,
                  validType: res.validType,
                  terminalLicenseCount: res.terminalLicenseCount,
                  licenseType: res.licenseType,
                  licenseProductInfo: res.licenseProductInfo,
                  licenseCodeInfo: res.licenseCodeInfo,
                  machineCode: res.machineCode,
                  applicant: res.applicant,
                  applyDate: res.applyDate,
                })
              ),
            },
          });
          submitLoading.value = false;
        });
      }
    });
  };

  // 取 消
  function cancelApply() {
    applyFormRef.value.resetFields();
    router.back();
  }
</script>

<style scoped>
  .form-container {
    display: flex;
    flex-direction: column;
    align-items: center;

    .el-form {
      width: 800px;
      .el-form-item {
        margin-bottom: 30px;
      }
    }
  }
  .documentation-upload >>> .el-upload {
    display: block;
  }
  .documentation-upload >>> .el-upload__tip {
    margin-top: 0;
  }

  .font-color {
    color: #606266;
  }
</style>

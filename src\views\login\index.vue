<template>
  <!-- 外部登录：直接跳转到外部登录页面 -->
  <div v-if="isExternalLogin" class="external-login-container">
    <div class="external-login-content">
      <div class="loading-wrapper">
        <el-icon class="loading-icon" size="48">
          <svg viewBox="0 0 1024 1024" class="loading-spin">
            <path fill="currentColor" d="M512 1024c-69.1 0-136.2-13.5-199.3-40.2C251.7 958 197 921 150.7 874.3S46 748.3 21.8 712.7C7.3 648.6-6.2 581.5 1.8 512c8-69.5 29.5-136.6 63.9-199.3C91.5 251.7 128.5 197 175.2 150.7S275.7 46 311.3 21.8C375.4 7.3 442.5-6.2 512 1.8c69.5 8 136.6 29.5 199.3 63.9C772.3 91.5 827 128.5 873.3 175.2S978 275.7 1002.2 311.3c14.5 64.1 28 131.2 20 200.7-8 69.5-29.5 136.6-63.9 199.3-25.8 61-62.8 115.7-109.5 162.4S748.3 978 712.7 1002.2c-64.1 14.5-131.2 28-200.7 20z"/>
          </svg>
        </el-icon>
        <h2 class="loading-title">正在跳转到登录页面...</h2>
        <p class="loading-subtitle">请稍候，系统正在为您跳转到统一身份认证平台</p>
        <div class="loading-progress">
          <el-progress :percentage="loadingProgress" :show-text="false" />
        </div>
      </div>
    </div>
  </div>

  <!-- 本地登录：显示账号密码登录表单 -->
  <div v-else class="login-container">
    <!-- 右侧切换主题、语言按钮  -->
    <div class="action-bar">
      <el-tooltip :content="t('login.themeToggle')" placement="bottom">
        <CommonWrapper>
          <DarkModeSwitch />
        </CommonWrapper>
      </el-tooltip>
      <el-tooltip :content="t('login.languageToggle')" placement="bottom">
        <CommonWrapper>
          <LangSelect size="text-20px" />
        </CommonWrapper>
      </el-tooltip>
    </div>
    <!-- 登录页主体 -->
    <div flex-1 flex-center>
      <div
        class="p-4xl w-full h-auto sm:w-450px border-rd-10px sm:h-680px shadow-[var(--el-box-shadow-light)] backdrop-blur-3px"
      >
        <div w-full flex flex-col items-center>
          <!-- logo -->
          <el-image :src="logo" style="width: 84px" />

          <!-- 标题 -->
          <h2>
            <el-badge :value="`v ${defaultSettings.version}`" type="success">
              {{ defaultSettings.title }}
            </el-badge>
          </h2>

          <!-- 组件切换 -->
          <transition name="fade-slide" mode="out-in">
            <component :is="formComponents[component]" v-model="component" class="w-90%" />
          </transition>
        </div>
      </div>
      <!-- 登录页底部版权 -->
      <el-text size="small" class="py-2.5! fixed bottom-0 text-center">
        Copyright © 2021 - 2025 youlai.tech All Rights Reserved.
        <a href="http://beian.miit.gov.cn/" target="_blank">皖ICP备20006496号-2</a>
      </el-text>
    </div>
  </div>
</template>

<script setup lang="ts">
import logo from "@/assets/logo.png";
import { defaultSettings } from "@/settings";
import CommonWrapper from "@/components/CommonWrapper/index.vue";
import DarkModeSwitch from "@/components/DarkModeSwitch/index.vue";

// 获取登录方式配置
const LOGIN_MODE = import.meta.env.VITE_LOGIN_MODE || 'local';
const APP_BASE_URL = ref(import.meta.env.VITE_APP_BASE_URL);
const SID_BASE_URL = ref(import.meta.env.VITE_SID_BASE_URL);

// 判断是否为外部登录
const isExternalLogin = computed(() => LOGIN_MODE === 'external');

// 加载进度
const loadingProgress = ref(0);

// 外部登录跳转逻辑
const redirectToExternalLogin = () => {
  const encodedAppBaseUrl = encodeURIComponent(APP_BASE_URL.value + "/#/cas");
  const redirectUrl = `${SID_BASE_URL.value}/login?service=${encodedAppBaseUrl}`;

  // 模拟加载进度
  const progressInterval = setInterval(() => {
    loadingProgress.value += 10;
    if (loadingProgress.value >= 100) {
      clearInterval(progressInterval);
      // 跳转到外部登录页面
      window.location.replace(redirectUrl);
    }
  }, 200);
};

// 如果是外部登录模式，在组件挂载前执行跳转
onBeforeMount(() => {
  if (isExternalLogin.value) {
    // 延迟一点时间显示加载动画
    setTimeout(() => {
      redirectToExternalLogin();
    }, 500);
  }
});

type LayoutMap = "login" | "register" | "resetPwd";

const t = useI18n().t;

const component = ref<LayoutMap>("login"); // 切换显示的组件
const formComponents = {
  login: defineAsyncComponent(() => import("./components/Login.vue")),
  register: defineAsyncComponent(() => import("./components/Register.vue")),
  resetPwd: defineAsyncComponent(() => import("./components/ResetPwd.vue")),
};
</script>

<style lang="scss" scoped>
// 外部登录容器样式
.external-login-container {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

.external-login-container::before {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 100%;
  content: "";
  background: url("@/assets/images/login-bg.svg");
  background-position: center center;
  background-size: cover;
  opacity: 0.1;
}

.external-login-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.loading-wrapper {
  text-align: center;
  padding: 60px 40px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-width: 400px;
  width: 90%;
}

.loading-icon {
  color: var(--el-color-primary);
  margin-bottom: 24px;
}

.loading-spin {
  animation: spin 2s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.loading-title {
  margin: 0 0 12px 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.loading-subtitle {
  margin: 0 0 32px 0;
  font-size: 16px;
  color: var(--el-text-color-regular);
  line-height: 1.5;
}

.loading-progress {
  margin-top: 24px;
}

// 本地登录容器样式
.login-container {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

// 添加伪元素作为背景层
.login-container::before {
  position: fixed;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 100%;
  content: "";
  background: url("@/assets/images/login-bg.svg");
  background-position: center center;
  background-size: cover;
}

.action-bar {
  position: fixed;
  top: 10px;
  right: 10px;
  z-index: 10;
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: center;
  font-size: 1.125rem;

  @media (max-width: 480px) {
    top: 10px;
    right: auto;
    left: 10px;
  }

  @media (min-width: 640px) {
    top: 40px;
    right: 40px;
  }
}

/* fade-slide */
.fade-slide-leave-active,
.fade-slide-enter-active {
  transition: all 0.3s;
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
</style>

<template>
  <div class="app-container">
    <div class="apply-page">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-content">
          <div class="header-icon">
            <el-icon size="32" color="var(--el-color-primary)">
              <svg viewBox="0 0 1024 1024">
                <path fill="currentColor" d="M832 384H576V128H192v768h640V384zm-26.496-64L640 154.496V320h165.504zM160 64h480l256 256v608a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V96a32 32 0 0 1 32-32z"/>
                <path fill="currentColor" d="M544 288H416a32 32 0 1 1 0-64h128a32 32 0 0 1 0 64zm144 128H288a32 32 0 1 1 0-64h400a32 32 0 0 1 0 64zm0 64H288a32 32 0 1 1 0-64h400a32 32 0 0 1 0 64zm0 64H288a32 32 0 1 1 0-64h400a32 32 0 0 1 0 64z"/>
              </svg>
            </el-icon>
          </div>
          <div class="header-text">
            <h1 class="page-title">授权码申请</h1>
            <p class="page-subtitle">请填写完整的授权信息，我们将为您生成专属的授权码</p>
          </div>
        </div>

        <!-- 进度指示器 -->
        <div class="progress-indicator">
          <el-steps :active="currentStep" finish-status="success" align-center>
            <el-step title="填写信息" description="完善授权申请信息"></el-step>
            <el-step title="提交申请" description="确认信息并提交"></el-step>
            <el-step title="申请成功" description="获取授权码文件"></el-step>
          </el-steps>
        </div>
      </div>

      <!-- 表单内容 -->
      <el-card class="form-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span class="card-title">授权申请信息</span>
            <el-tag type="info" size="small">必填信息</el-tag>
          </div>
        </template>

        <el-form
          ref="applyFormRef"
          :model="applyFormData"
          :rules="applyFormRules"
          label-width="140px"
          class="apply-form"
          :validate-on-rule-change="false"
        >
          <!-- 基本信息分组 -->
          <div class="form-section">
            <h3 class="section-title">
              <el-icon class="section-icon">
                <svg viewBox="0 0 1024 1024">
                  <path fill="currentColor" d="M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896zm23.744 191.488c-52.096 0-92.928 14.784-123.2 44.352-30.976 29.568-45.76 70.4-45.76 122.496h80.256c0-29.568 5.632-52.8 17.6-68.992 13.376-19.712 35.2-28.864 66.176-28.864 23.936 0 42.944 6.336 56.32 19.712 12.672 13.376 19.712 31.68 19.712 54.912 0 17.6-6.336 34.496-19.008 49.984l-8.448 9.856c-45.76 40.832-73.216 70.4-82.368 89.408-9.856 19.008-14.08 42.24-14.08 68.992v9.856h80.96v-9.856c0-16.896 3.52-31.68 10.56-44.352 6.336-12.672 15.488-24.64 28.16-35.2 33.792-29.568 54.208-48.576 60.544-57.728 14.08-20.416 21.12-42.944 21.12-68.288 0-53.504-15.488-95.232-47.168-124.8-31.68-29.568-74.624-44.352-128.32-44.352z"/>
                  <path fill="currentColor" d="M512 704a80 80 0 1 1 0 160 80 80 0 0 1 0-160z"/>
                </svg>
              </el-icon>
              基本信息
            </h3>

            <el-row :gutter="24">
              <el-col :lg="12" :md="24">
                <el-form-item label="授权类型" prop="licenseType">
                  <el-select
                    v-model="applyFormData.licenseType"
                    placeholder="请选择授权类型"
                    clearable
                    size="large"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in LICENSE_TYPE_OPTIONS"
                      :key="item"
                      :label="item"
                      :value="item"
                    >
                      <span style="float: left">{{ item }}</span>
                      <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">
                        {{ getLicenseTypeDesc(item) }}
                      </span>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :lg="12" :md="24">
                <el-form-item label="有效期" prop="validType">
                  <el-select
                    v-model="applyFormData.validType"
                    placeholder="请选择有效期"
                    size="large"
                    style="width: 100%"
                  >
                    <el-option label="永久" value="永久">
                      <span style="float: left">永久</span>
                      <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">
                        无时间限制
                      </span>
                    </el-option>
                    <el-option label="1年" value="1年">
                      <span style="float: left">1年</span>
                      <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">
                        365天有效期
                      </span>
                    </el-option>
                    <el-option label="2年" value="2年">
                      <span style="float: left">2年</span>
                      <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">
                        730天有效期
                      </span>
                    </el-option>
                    <el-option label="3年" value="3年">
                      <span style="float: left">3年</span>
                      <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">
                        1095天有效期
                      </span>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 项目信息分组 -->
          <div class="form-section">
            <h3 class="section-title">
              <el-icon class="section-icon">
                <svg viewBox="0 0 1024 1024">
                  <path fill="currentColor" d="M160 224a64 64 0 0 0-64 64v448a64 64 0 0 0 64 64h704a64 64 0 0 0 64-64V288a64 64 0 0 0-64-64H160zm0-64h704a128 128 0 0 1 128 128v448a128 128 0 0 1-128 128H160A128 128 0 0 1 32 736V288A128 128 0 0 1 160 160z"/>
                  <path fill="currentColor" d="M320 400a32 32 0 0 1 32-32h320a32 32 0 0 1 0 64H352a32 32 0 0 1-32-32zm0 128a32 32 0 0 1 32-32h320a32 32 0 0 1 0 64H352a32 32 0 0 1-32-32z"/>
                </svg>
              </el-icon>
              项目信息
            </h3>

            <el-row :gutter="24">
              <el-col :lg="12" :md="24">
                <el-form-item label="销售订单编号" prop="salesOrderNo">
                  <el-input
                    v-model="applyFormData.salesOrderNo"
                    placeholder="请输入销售订单编号"
                    clearable
                    size="large"
                    maxlength="50"
                    show-word-limit
                  >
                    <template #prefix>
                      <el-icon>
                        <svg viewBox="0 0 1024 1024">
                          <path fill="currentColor" d="M832 384H576V128H192v768h640V384zm-26.496-64L640 154.496V320h165.504zM160 64h480l256 256v608a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V96a32 32 0 0 1 32-32z"/>
                        </svg>
                      </el-icon>
                    </template>
                  </el-input>
                </el-form-item>
              </el-col>

              <el-col :lg="12" :md="24">
                <el-form-item label="项目销售" prop="sellerName">
                  <el-input
                    v-model="applyFormData.sellerName"
                    placeholder="请输入项目销售姓名"
                    clearable
                    size="large"
                    maxlength="20"
                    show-word-limit
                  >
                    <template #prefix>
                      <el-icon>
                        <svg viewBox="0 0 1024 1024">
                          <path fill="currentColor" d="M512 512a192 192 0 1 0 0-384 192 192 0 0 0 0 384zm0 64a256 256 0 1 1 0-512 256 256 0 0 1 0 512z"/>
                          <path fill="currentColor" d="M512 512a160 160 0 0 1 160 160v320H352V672a160 160 0 0 1 160-160z"/>
                        </svg>
                      </el-icon>
                    </template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="销售项目名称" prop="salesProjectName">
              <el-input
                v-model="applyFormData.salesProjectName"
                placeholder="请输入销售项目名称"
                clearable
                size="large"
                maxlength="100"
                show-word-limit
              >
                <template #prefix>
                  <el-icon>
                    <svg viewBox="0 0 1024 1024">
                      <path fill="currentColor" d="M160 224a64 64 0 0 0-64 64v448a64 64 0 0 0 64 64h704a64 64 0 0 0 64-64V288a64 64 0 0 0-64-64H160zm0-64h704a128 128 0 0 1 128 128v448a128 128 0 0 1-128 128H160A128 128 0 0 1 32 736V288A128 128 0 0 1 160 160z"/>
                    </svg>
                  </el-icon>
                </template>
              </el-input>
            </el-form-item>

            <el-form-item label="最终客户名称" prop="finalCustomerName">
              <el-input
                v-model="applyFormData.finalCustomerName"
                placeholder="请输入最终客户名称"
                clearable
                size="large"
                maxlength="100"
                show-word-limit
              >
                <template #prefix>
                  <el-icon>
                    <svg viewBox="0 0 1024 1024">
                      <path fill="currentColor" d="M192 128a128 128 0 1 0 0 256 128 128 0 0 0 0-256zm0 320a192 192 0 1 1 0-384 192 192 0 0 1 0 384z"/>
                      <path fill="currentColor" d="M544 128a128 128 0 1 0 0 256 128 128 0 0 0 0-256zm0 320a192 192 0 1 1 0-384 192 192 0 0 1 0 384z"/>
                      <path fill="currentColor" d="M192 544a128 128 0 1 0 0 256 128 128 0 0 0 0-256zm0 320a192 192 0 1 1 0-384 192 192 0 0 1 0 384z"/>
                      <path fill="currentColor" d="M544 544a128 128 0 1 0 0 256 128 128 0 0 0 0-256zm0 320a192 192 0 1 1 0-384 192 192 0 0 1 0 384z"/>
                    </svg>
                  </el-icon>
                </template>
              </el-input>
            </el-form-item>
          </div>

          <!-- 并发数量配置分组 -->
          <div class="form-section">
            <h3 class="section-title">
              <el-icon class="section-icon">
                <svg viewBox="0 0 1024 1024">
                  <path fill="currentColor" d="M224 128v704h576V128H224zm-32-64h640a32 32 0 0 1 32 32v768a32 32 0 0 1-32 32H192a32 32 0 0 1-32-32V96a32 32 0 0 1 32-32z"/>
                  <path fill="currentColor" d="M288 192h448v64H288v-64zm0 128h448v64H288v-64zm0 128h448v64H288v-64zm0 128h448v64H288v-64zm0 128h448v64H288v-64z"/>
                </svg>
              </el-icon>
              并发数量配置
            </h3>

            <el-form-item label="账号并发登录数量" prop="permitUserCnt" class="concurrent-config">
              <div class="concurrent-inputs">
                <div class="input-group">
                  <label class="input-label">普通账号（学生）</label>
                  <el-input-number
                    v-model="applyFormData.permitUserCnt"
                    :min="1"
                    :max="500"
                    :precision="0"
                    size="large"
                    controls-position="right"
                    placeholder="请输入数量"
                  />
                  <span class="unit-text">个</span>
                </div>

                <div class="plus-symbol">
                  <el-icon size="20" color="var(--el-color-primary)">
                    <svg viewBox="0 0 1024 1024">
                      <path fill="currentColor" d="M480 480V128a32 32 0 0 1 64 0v352h352a32 32 0 1 1 0 64H544v352a32 32 0 1 1-64 0V544H128a32 32 0 0 1 0-64h352z"/>
                    </svg>
                  </el-icon>
                </div>

                <div class="input-group">
                  <label class="input-label">管理员账号（老师）</label>
                  <el-input-number
                    v-model="applyFormData.permitMgrCnt"
                    :min="1"
                    :max="100"
                    :precision="0"
                    size="large"
                    controls-position="right"
                    placeholder="请输入数量"
                  />
                  <span class="unit-text">个</span>
                </div>
              </div>

              <div class="total-display">
                <el-text type="primary" size="large">
                  总并发数量：{{ (applyFormData.permitUserCnt || 0) + (applyFormData.permitMgrCnt || 0) }} 个
                </el-text>
              </div>

              <el-alert
                type="info"
                :closable="false"
                show-icon
                class="concurrent-tip"
              >
                <template #title>
                  <span>并发说明</span>
                </template>
                <p>• 允许创建超出并发登录数量的账号</p>
                <p>• 同时登录的账号上限为设置的并发登录数量</p>
                <p>• 普通账号适用于学生用户，管理员账号适用于教师用户</p>
              </el-alert>
            </el-form-item>
          </div>

          <!-- 证明文件上传（条件显示） -->
          <div v-if="applyFormData.licenseType === '变更授权'" class="form-section">
            <h3 class="section-title">
              <el-icon class="section-icon">
                <svg viewBox="0 0 1024 1024">
                  <path fill="currentColor" d="M544 864V672h128L512 480 352 672h128v192H320v-1.6c-5.376.32-10.496 1.6-16 1.6A240 240 0 0 1 64 624c0-123.136 93.12-223.488 212.608-237.248A239.808 239.808 0 0 1 512 192a239.872 239.872 0 0 1 235.456 194.752c119.488 13.76 212.48 114.112 212.48 237.248a240 240 0 0 1-240 240c-5.376 0-10.56-1.28-16-1.6v1.6H544z"/>
                </svg>
              </el-icon>
              证明文件
            </h3>

            <el-form-item label="上传证明文件" prop="documentationList">
              <div class="upload-container">
                <el-upload
                  class="documentation-upload"
                  :on-remove="handleRemove"
                  :before-remove="beforeRemove"
                  :on-change="handleChange"
                  :file-list="applyFormData.documentationList"
                  :auto-upload="false"
                  accept=".jpg,.jpeg,.png,.pdf"
                  drag
                  multiple
                  :limit="3"
                >
                  <div class="upload-content">
                    <el-icon size="48" color="var(--el-color-primary)">
                      <svg viewBox="0 0 1024 1024">
                        <path fill="currentColor" d="M544 864V672h128L512 480 352 672h128v192H320v-1.6c-5.376.32-10.496 1.6-16 1.6A240 240 0 0 1 64 624c0-123.136 93.12-223.488 212.608-237.248A239.808 239.808 0 0 1 512 192a239.872 239.872 0 0 1 235.456 194.752c119.488 13.76 212.48 114.112 212.48 237.248a240 240 0 0 1-240 240c-5.376 0-10.56-1.28-16-1.6v1.6H544z"/>
                      </svg>
                    </el-icon>
                    <div class="upload-text">
                      <p class="upload-title">点击或拖拽文件到此区域上传</p>
                      <p class="upload-subtitle">支持 JPG、PNG、PDF 格式，单个文件不超过 5MB</p>
                    </div>
                  </div>
                </el-upload>
              </div>
            </el-form-item>
          </div>
        </el-form>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button
            type="primary"
            size="large"
            :loading="submitLoading"
            @click="handleSubmit()"
            class="submit-btn"
          >
            <el-icon class="mr-2">
              <svg viewBox="0 0 1024 1024">
                <path fill="currentColor" d="M832 384H576V128H192v768h640V384zm-26.496-64L640 154.496V320h165.504zM160 64h480l256 256v608a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V96a32 32 0 0 1 32-32z"/>
                <path fill="currentColor" d="M544 288H416a32 32 0 1 1 0-64h128a32 32 0 0 1 0 64zm144 128H288a32 32 0 1 1 0-64h400a32 32 0 0 1 0 64zm0 64H288a32 32 0 1 1 0-64h400a32 32 0 0 1 0 64zm0 64H288a32 32 0 1 1 0-64h400a32 32 0 0 1 0 64z"/>
              </svg>
            </el-icon>
            {{ submitLoading ? '提交中...' : '提交申请' }}
          </el-button>

          <el-button
            size="large"
            @click="cancelApply()"
            class="cancel-btn"
          >
            <el-icon class="mr-2">
              <svg viewBox="0 0 1024 1024">
                <path fill="currentColor" d="M224 480h640a32 32 0 1 1 0 64H224a32 32 0 0 1 0-64z"/>
                <path fill="currentColor" d="m237.248 512 265.408 265.344a32 32 0 0 1-45.312 45.312l-288-288a32 32 0 0 1 0-45.312l288-288a32 32 0 1 1 45.312 45.312L237.248 512z"/>
              </svg>
            </el-icon>
            取消
          </el-button>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive } from "vue";
  import { useRouter } from "vue-router";
  import LicenseCodeAPI from "@/api/license-manage/license-code.api";
  import { ApplyForm, ApplySuccessInfo } from "@/api/license-manage/types/license-code.type";
  import useUpload from "@/hooks/license/useUpload";
  import { LICENSE_TYPE_OPTIONS } from "@/constants";
  import Base64 from "@/utils/base64";

  const submitLoading = ref(false);
  const applyFormRef = ref();
  const router = useRouter();

  // 当前步骤
  const currentStep = ref(0);

  const applyFormData = reactive<ApplyForm>({
    licenseType: "",
    salesOrderNo: "",
    salesProjectName: "",
    finalCustomerName: "",
    sellerName: "",
    validType: "永久",
    permitUserCnt: 1,
    permitMgrCnt: 1,
    documentationList: [],
  });

  // 文件上传
  const { beforeRemove, handleRemove, handleChange } = useUpload(applyFormData);

  // 获取授权类型描述
  const getLicenseTypeDesc = (type: string) => {
    const descMap: Record<string, string> = {
      '新项目授权': '全新项目',
      '变更授权': '需要文件',
      '测试授权': '临时使用'
    };
    return descMap[type] || '';
  };

  // 表单校验规则
  const applyFormRules = reactive({
    licenseType: [{ required: true, message: "请选择授权类型", trigger: "change" }],
    salesOrderNo: [
      { required: true, message: "请输入销售订单编号", trigger: "blur" },
      { min: 3, max: 50, message: "长度在 3 到 50 个字符", trigger: "blur" }
    ],
    salesProjectName: [
      { required: true, message: "请输入销售项目名称", trigger: "blur" },
      { min: 2, max: 100, message: "长度在 2 到 100 个字符", trigger: "blur" }
    ],
    finalCustomerName: [
      { required: true, message: "请输入最终客户名称", trigger: "blur" },
      { min: 2, max: 100, message: "长度在 2 到 100 个字符", trigger: "blur" }
    ],
    sellerName: [
      { required: true, message: "请输入项目销售姓名", trigger: "blur" },
      { min: 2, max: 20, message: "长度在 2 到 20 个字符", trigger: "blur" }
    ],
    validType: [{ required: true, message: "请选择有效期", trigger: "change" }],
    permitUserCnt: [
      { required: true, message: "请输入普通账号并发数量", trigger: "blur" }
    ],
    permitMgrCnt: [
      { required: true, message: "请输入管理员账号并发数量", trigger: "blur" }
    ],
    documentationList: [
      {
        required: true,
        message: "请上传证明文件",
        trigger: "change",
        validator: (_rule: any, value: any, callback: any) => {
          if (applyFormData.licenseType === '变更授权' && (!value || value.length === 0)) {
            callback(new Error('变更授权需要上传证明文件'));
          } else {
            callback();
          }
        }
      }
    ],
  });

  // 提交申请
  const handleSubmit = () => {
    currentStep.value = 1; // 设置为提交步骤

    applyFormRef.value.validate((valid: boolean) => {
      if (valid) {
        submitLoading.value = true;
        const formParam = new FormData();

        // 处理文件上传
        if (applyFormData.documentationList.length > 0 && applyFormData.documentationList[0].raw) {
          formParam.append("documentation", applyFormData.documentationList[0].raw);
        }

        // 添加表单数据
        formParam.append("licenseType", applyFormData.licenseType);
        formParam.append("salesOrderNo", applyFormData.salesOrderNo);
        formParam.append("salesProjectName", applyFormData.salesProjectName);
        formParam.append("finalCustomerName", applyFormData.finalCustomerName);
        formParam.append("sellerName", applyFormData.sellerName);
        formParam.append("validType", applyFormData.validType);
        formParam.append("permitUserCnt", applyFormData.permitUserCnt.toString());
        formParam.append("permitMgrCnt", applyFormData.permitMgrCnt.toString());

        LicenseCodeAPI.apply(formParam)
          .then((res: ApplySuccessInfo) => {
            submitLoading.value = false;
            currentStep.value = 2; // 设置为成功步骤

            // 跳转到成功页面
            router.push({
              path: "/license-code/apply-success",
              query: {
                info: Base64.encode(
                  JSON.stringify({
                    id: res.id,
                    salesOrderNo: res.salesOrderNo,
                    salesProjectName: res.salesProjectName,
                    finalCustomerName: res.finalCustomerName,
                    sellerName: res.sellerName,
                    validType: res.validType,
                    terminalLicenseCount: res.terminalLicenseCount,
                    licenseType: res.licenseType,
                    licenseProductInfo: res.licenseProductInfo,
                    licenseCodeInfo: res.licenseCodeInfo,
                    applicant: res.applicant,
                    applyDate: res.applyDate,
                  })
                ),
              },
            });
          })
          .catch(() => {
            submitLoading.value = false;
            currentStep.value = 0; // 重置为填写信息步骤
          });
      } else {
        currentStep.value = 0; // 重置为填写信息步骤
      }
    });
  };

  // 取消申请
  function cancelApply() {
    applyFormRef.value.resetFields();
    router.back();
  }
</script>

<style scoped>
.apply-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  margin-bottom: 32px;
}

.header-content {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.header-icon {
  margin-right: 16px;
  padding: 12px;
  background: var(--el-color-primary-light-9);
  border-radius: 12px;
}

.header-text {
  flex: 1;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.page-subtitle {
  margin: 0;
  font-size: 16px;
  color: var(--el-text-color-regular);
}

.progress-indicator {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.form-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.form-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.apply-form {
  padding: 24px;
}

.form-section {
  margin-bottom: 32px;
  padding: 24px;
  background: var(--el-fill-color-lighter);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-color-primary);
}

.section-icon {
  margin-right: 8px;
  font-size: 18px;
}

.concurrent-config {
  margin-bottom: 0;
}

.concurrent-inputs {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 16px;
}

.input-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-regular);
}

.unit-text {
  margin-left: 8px;
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.plus-symbol {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--el-color-primary-light-9);
  border-radius: 50%;
  margin-top: 20px;
}

.total-display {
  text-align: center;
  margin-bottom: 16px;
  padding: 12px;
  background: var(--el-color-primary-light-9);
  border-radius: 6px;
}

.concurrent-tip {
  margin-bottom: 0;
}

.concurrent-tip p {
  margin: 4px 0;
  font-size: 13px;
}

.upload-container {
  width: 100%;
}

.documentation-upload {
  width: 100%;
}

:deep(.documentation-upload .el-upload) {
  width: 100%;
}

:deep(.documentation-upload .el-upload-dragger) {
  width: 100%;
  height: 180px;
  border: 2px dashed var(--el-border-color);
  border-radius: 8px;
  background: var(--el-fill-color-blank);
  transition: all 0.3s ease;
}

:deep(.documentation-upload .el-upload-dragger:hover) {
  border-color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 20px;
}

.upload-text {
  margin-top: 16px;
  text-align: center;
}

.upload-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.upload-subtitle {
  margin: 0;
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 32px 24px 24px;
  border-top: 1px solid var(--el-border-color-lighter);
  background: var(--el-fill-color-lighter);
}

.submit-btn {
  min-width: 140px;
  height: 44px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(64, 128, 255, 0.3);
  transition: all 0.3s ease;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(64, 128, 255, 0.4);
}

.cancel-btn {
  min-width: 100px;
  height: 44px;
  font-size: 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  transform: translateY(-1px);
}

.mr-2 {
  margin-right: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .apply-page {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    text-align: center;
  }

  .header-icon {
    margin-right: 0;
    margin-bottom: 16px;
  }

  .page-title {
    font-size: 24px;
  }

  .progress-indicator {
    padding: 16px;
  }

  .apply-form {
    padding: 16px;
  }

  .form-section {
    padding: 16px;
  }

  .concurrent-inputs {
    flex-direction: column;
    gap: 16px;
  }

  .plus-symbol {
    margin-top: 0;
    transform: rotate(90deg);
  }

  .form-actions {
    flex-direction: column;
    align-items: center;
    gap: 12px;
    padding: 24px 16px;
  }

  .submit-btn,
  .cancel-btn {
    width: 100%;
    max-width: 280px;
  }
}

@media (max-width: 480px) {
  .apply-page {
    padding: 12px;
  }

  .page-title {
    font-size: 20px;
  }

  .page-subtitle {
    font-size: 14px;
  }

  .section-title {
    font-size: 14px;
  }

  .progress-indicator {
    padding: 12px;
  }

  :deep(.el-steps) {
    --el-step-font-size: 12px;
  }
}
</style>

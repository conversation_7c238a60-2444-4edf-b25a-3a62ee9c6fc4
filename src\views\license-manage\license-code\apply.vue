<template>
  <div class="app-container">
    <el-card header="授权码申请">
      <div class="form-container">
        <el-form
          ref="applyFormRef"
          :model="applyFormData"
          :rules="applyFormRules"
          label-width="140px"
        >
          <el-form-item label="授权类型" prop="licenseType">
            <el-select
              v-model="applyFormData.licenseType"
              placeholder="请输入选择授权类型"
              clearable
            >
              <el-option
                v-for="item in LICENSE_TYPE_OPTIONS"
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="销售订单编号" prop="salesOrderNo">
            <el-input
              v-model="applyFormData.salesOrderNo"
              placeholder="请输入销售订单编号"
              clearable
            />
          </el-form-item>

          <el-form-item label="销售项目名称" prop="salesProjectName">
            <el-input
              v-model="applyFormData.salesProjectName"
              placeholder="请输入销售项目名称"
              clearable
            />
          </el-form-item>

          <el-form-item label="最终客户名称" prop="finalCustomerName">
            <el-input
              v-model="applyFormData.finalCustomerName"
              placeholder="请输入最终客户名称"
              clearable
            />
          </el-form-item>

          <el-form-item label="项目销售" prop="sellerName">
            <el-input v-model="applyFormData.sellerName" placeholder="请输入项目销售" clearable />
          </el-form-item>

          <el-form-item label="有效期" prop="validType">
            <el-input v-model="applyFormData.validType" placeholder="请输入有效期" clearable />
          </el-form-item>

          <el-form-item label="账号并发登录数量" prop="permitUserCnt">
            <el-row type="flex" justify="space-between">
              <el-col :span="11">
                <el-form-item prop="permitUserCnt" style="margin-bottom: 0">
                  <el-input-number
                    v-model="applyFormData.permitUserCnt"
                    :min="1"
                    :max="50"
                    :precision="0"
                    label="请输入普通账号并发数量（学生）"
                  ></el-input-number>
                </el-form-item>
              </el-col>
              <span class="font-color">＋</span>
              <el-col :span="11">
                <el-form-item prop="permitMgrCnt" style="margin-bottom: 0">
                  <el-input-number
                    v-model="applyFormData.permitMgrCnt"
                    :min="1"
                    :max="50"
                    :precision="0"
                    label="请输入管理员账号并发数量（老师）"
                  ></el-input-number>
                </el-form-item>
              </el-col>
            </el-row>
            <span class="font-color">
              备注说明：允许创建超出并发登录数量的账号，但是同时登录的账号上限为账号并发登录数量
            </span>
          </el-form-item>
          <el-form-item
            v-if="applyFormData.licenseType === '变更授权'"
            label="证明文件"
            prop="documentationList"
          >
            <el-upload
              class="documentation-upload"
              :on-remove="handleRemove"
              :before-remove="beforeRemove"
              :on-change="handleChange"
              :file-list="applyFormData.documentationList"
              :auto-upload="false"
              accept=".jpg,.png,.pdf"
            >
              <el-button size="small" type="primary">点击上传</el-button>
              <template #tip>
                <div class="el-upload__tip">请上传jpg/png/PDF格式文件，且不超过5000kb</div>
              </template>
            </el-upload>
          </el-form-item>
        </el-form>
        <div>
          <el-button type="primary" @click="handleSubmit()" :loading="submitLoading">
            提交申请
          </el-button>
          <el-button @click="cancelApply()">取 消</el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive } from "vue";
  import { useRouter } from "vue-router";
  import LicenseCodeAPI from "@/api/license-manage/license-code.api";
  import { ApplyForm, ApplySuccessInfo } from "@/api/license-manage/types/license-code.type";
  import useUpload from "@/hooks/license/useUpload";
  import { LICENSE_TYPE_OPTIONS } from "@/constants";
  import Base64 from "@/utils/base64";

  const submitLoading = ref(false);
  const applyFormRef = ref();
  const router = useRouter();
  const applyFormData = reactive<ApplyForm>({
    licenseType: "",
    salesOrderNo: "",
    salesProjectName: "",
    finalCustomerName: "",
    sellerName: "",
    validType: "永久",
    permitUserCnt: 1,
    permitMgrCnt: 1,
    documentationList: [],
  });

  // 文件上传
  const { beforeRemove, handleRemove, handleChange } = useUpload(applyFormData);

  // 表单校验
  const applyFormRules = reactive({
    licenseType: [{ required: true, message: "请选择授权类型", trigger: "blur" }],
    salesOrderNo: [{ required: true, message: "请输入销售订单编号", trigger: "blur" }],
    salesProjectName: [{ required: true, message: "请输入销售项目名称", trigger: "blur" }],
    finalCustomerName: [{ required: true, message: "请输入最终客户名称", trigger: "blur" }],
    sellerName: [{ required: true, message: "请输入项目销售", trigger: "blur" }],
    validType: [{ required: true, message: "请输入有效期", trigger: "blur" }],
    permitUserCnt: [{ required: true, message: "请输入普通账号并发数量", trigger: "blur" }],
    permitMgrCnt: [{ required: true, message: "请输入管理员账号并发数量", trigger: "blur" }],
    documentationList: [{ required: true, message: "请上传证明文件", trigger: "blur" }],
  });

  // 提交申请
  const handleSubmit = () => {
    applyFormRef.value.validate((valid: boolean) => {
      if (valid) {
        submitLoading.value = true;
        const formParam = new FormData();
        if (applyFormData.documentationList.length > 0 && applyFormData.documentationList[0].raw) {
          formParam.append("documentation", applyFormData.documentationList[0].raw);
        }
        formParam.append("licenseType", applyFormData.licenseType);
        formParam.append("salesOrderNo", applyFormData.salesOrderNo);
        formParam.append("salesProjectName", applyFormData.salesProjectName);
        formParam.append("finalCustomerName", applyFormData.finalCustomerName);
        formParam.append("sellerName", applyFormData.sellerName);
        formParam.append("validType", applyFormData.validType);
        formParam.append("permitUserCnt", applyFormData.permitUserCnt.toString());
        formParam.append("permitMgrCnt", applyFormData.permitMgrCnt.toString());
        LicenseCodeAPI.apply(formParam).then((res: ApplySuccessInfo) => {
          submitLoading.value = false;
          router.push({
            path: "/license-code/apply-success",
            query: {
              info: Base64.encode(
                JSON.stringify({
                  id: res.id,
                  salesOrderNo: res.salesOrderNo,
                  salesProjectName: res.salesProjectName,
                  finalCustomerName: res.finalCustomerName,
                  sellerName: res.sellerName,
                  validType: res.validType,
                  terminalLicenseCount: res.terminalLicenseCount,
                  licenseType: res.licenseType,
                  licenseProductInfo: res.licenseProductInfo,
                  licenseCodeInfo: res.licenseCodeInfo,
                  applicant: res.applicant,
                  applyDate: res.applyDate,
                })
              ),
            },
          });
        });
      }
    });
  };

  // 取 消
  function cancelApply() {
    applyFormRef.value.resetFields();
    router.back();
  }
</script>

<style scoped>
  .form-container {
    display: flex;
    flex-direction: column;
    align-items: center;

    .el-form {
      width: 800px;
      .el-form-item {
        margin-bottom: 30px;
      }
    }
  }
  .documentation-upload >>> .el-upload {
    display: block;
  }
  .documentation-upload >>> .el-upload__tip {
    margin-top: 0;
  }

  .font-color {
    color: #606266;
  }
</style>
